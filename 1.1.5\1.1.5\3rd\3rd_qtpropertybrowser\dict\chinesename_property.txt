#枚举值部分
pieStyle,饼图样式
PieStyle_Three,三色圆环
PieStyle_Current,当前圆环

pointerStyle,指针样式
PointerStyle_Circle,圆形指示器
PointerStyle_Indicator,指针指示器
PointerStyle_IndicatorR,圆角指针指示器
PointerStyle_Triangle,三角形指示器

bannerStyle,指示器样式
BannerStyle_Min,迷你型样式
BannerStyle_Num,数字型样式

navPosition,指示器位置
NavPosition_Left,左边位置
NavPosition_Center,中间位置
NavPosition_Right,右侧位置

navStyle,导航样式
NavStyle_Ellipse,椭圆条状
NavStyle_Circle,圆形
NavStyle_Rect,矩形
NavStyle_Dot,小圆点
NavStyle_LongRect,长条状

NavStyle_JD,京东样式
NavStyle_TB,淘宝样式
NavStyle_ZFB,支付宝样式

lineStyle,线条样式
LineStyle_Rect,矩形
LineStyle_RoundedRect,圆角矩形
LineStyle_Ellipse,椭圆
LineStyle_Circle,圆形

buttonStyle,按钮样式
ButtonStyle_Circle,圆形
ButtonStyle_Police,警察
ButtonStyle_Bubble,气泡
ButtonStyle_Bubble2,气泡2
ButtonStyle_Msg,消息
ButtonStyle_Msg2,消息2

ButtonStyle_Rect,圆角矩形
ButtonStyle_CircleIn,内圆形
ButtonStyle_CircleOut,外圆形

ButtonStyle_1,开关样式1
ButtonStyle_2,开关样式2
ButtonStyle_3,开关样式3

buttonStatus,按钮状态
ButtonStatus_Arming,布防
ButtonStatus_Disarming,撤防
ButtonStatus_Alarm,报警
ButtonStatus_Bypass,旁路
ButtonStatus_Error,故障

buttonColor,按钮颜色
ButtonColor_Green,绿色
ButtonColor_Blue,蓝色
ButtonColor_Red,红色
ButtonColor_Gray,灰色
ButtonColor_Black,黑色

linePosition,线条位置
LinePosition_Left,左边
LinePosition_Right,右边
LinePosition_Top,上边
LinePosition_Bottom,下边
LinePosition_Left,左侧
LinePosition_Right,右侧
LinePosition_Top,顶部
LinePosition_Bottom,底部

colorMode,背景色模式
ColorMode_Normal,松开按下两种颜色
ColorMode_Replace,按下松开颜色上下交替
ColorMode_Shade,按下松开颜色渐变交替

textAlign,文字对齐
TextAlign_Left,左侧对齐
TextAlign_Right,右侧对齐
TextAlign_Top,顶部对齐
TextAlign_Bottom,底部对齐
TextAlign_Center,居中对齐

TextAlign_Top_Left,顶部左侧
TextAlign_Top_Center,顶部居中
TextAlign_Top_Right,顶部右侧
TextAlign_Center_Left,中间左侧
TextAlign_Center_Center,中间居中
TextAlign_Center_Right,中间右侧
TextAlign_Bottom_Left,底部左侧
TextAlign_Bottom_Center,底部居中
TextAlign_Bottom_Right,底部右侧

secondStyle,秒钟样式
SecondStyle_Normal,普通效果
SecondStyle_Spring,弹簧效果
SecondStyle_Continue,连续效果
SecondStyle_Hide,隐藏效果

cloudStyle,云台样式
CloudStyle_Black,黑色风格
CloudStyle_White,白色风格
CloudStyle_Blue,蓝色风格
CloudStyle_Purple,紫色风格
CloudStyle_Custom,自定义风格

scaleStyle,刻度尺样式
ScaleStyle_Value,跟随值变化
ScaleStyle_Time,设置值变化
ScaleStyle_Single,单向标尺
ScaleStyle_Double,双向标尺

rangeStyle,范围值样式
RangeStyle_Line,线条状风格
RangeStyle_Circle,圆形状风格

weatherType,天气类型
WeatherType_SUNNY,晴天
WeatherType_RAINY,雨天
WeatherType_SNOWY,雪天
WeatherType_CLOUDY,多云
WeatherType_WINDY,风
WeatherType_SNOWY_RAINY,雪雨
WeatherType_HAIL,冰雹
WeatherType_LIGHTNING,闪电
WeatherType_FOG,雾
WeatherType_PARTLYCLOUDY,局部多云

FadeEffect,渐变渐显
BlindsEffect,百叶窗
FlipRightToLeft,从右向左翻转
OutsideToInside,从外到内水平分割
MoveLeftToRightEffect,从左到右转动
MoveRightToLeftEffect,从右到左转动
MoveBottomToUpEffect,从下到上转动
MoveUpToBottomEffect,从上到下转动
MoveBottomToLeftUpEffect,从右下到左上飞入

calendarStyle,日历风格
CalendarStyle_Yellow,黄色风格
CalendarStyle_Blue,蓝色风格
CalendarStyle_Brown,褐色风格
CalendarStyle_Gray,灰色风格
CalendarStyle_Purple,紫色风格
CalendarStyle_Red,红色风格

clockStyle,钟表样式
ClockStyle_Trad,黑色风格
ClockStyle_System,银色风格
ClockStyle_Modern,红色风格
ClockStyle_Flower,花瓣风格

pilotStyle,指示灯风格
PilotStyle_Blue,蓝色风格
PilotStyle_Gray,灰色风格
PilotStyle_Green,绿色风格
PilotStyle_Red,红色风格
PilotStyle_Yellow,黄色风格

dayType,当前日类型
DayType_MonthPre,上月剩余天数
DayType_MonthNext,下个月的天数
DayType_MonthCurrent,当月天数
DayType_WeekEnd,周末

selectType,选中模式
SelectType_Rect,矩形背景
SelectType_Circle,圆形背景
SelectType_Triangle,带三角标
SelectType_Image,图片背景

weekNameFormat,星期格式
WeekNameFormat_Short,短名称
WeekNameFormat_Normal,普通名称
WeekNameFormat_Long,长名称
WeekNameFormat_En,英文名称

barStyle,风格样式
BarStyle_Rect,圆角矩形
BarStyle_Line_Top,上边线条
BarStyle_Line_Right,右边线条
BarStyle_Line_Bottom,下边线条
BarStyle_Line_Left,左边线条

BarStyle_Donut,圆环风格
BarStyle_Pie,饼状风格
BarStyle_Line,线条风格
BarStyle_Arc,圆弧状风格
BarStyle_RoundCircle,旋转圆风格
BarStyle_Pie,三角圆弧风格
BarStyle_Line,线条风格
BarStyle_Ring,圆环风格
BarStyle_SingleCircle,一个圆闪烁
BarStyle_DoubleCircle,两个圆闪烁

trianglePosition,三角形位置
TrianglePosition_Left,左侧
TrianglePosition_Right,右侧
TrianglePosition_Top,顶部
TrianglePosition_Bottom,底部
TrianglePosition_None,无

iconPosition,图标位置
IconPosition_Left,左侧
IconPosition_Right,右侧
IconPosition_Top,顶部
IconPosition_Bottom,底部

arrowPosition,箭头位置
ArrowPosition_Left,向左箭头
ArrowPosition_Right,向右箭头
ArrowPosition_Top,向上箭头
ArrowPosition_Bottom,向下箭头

expendMode,节点展开模式
ExpendMode_SingleClick,单击展开
ExpendMode_DoubleClick,双击展开
ExpendMode_NoClick,禁用单击

alignment,对齐方式
Alignment_Left,左对齐
Alignment_Center,居中对齐
Alignment_Right,右对齐

percentStyle,进度样式风格
PercentStyle_Arc,圆弧风格
PercentStyle_Polo,水池风格
PercentStyle_Wave,波纹风格
PercentStyle_Arc_Polo,圆弧水池风格
PercentStyle_Arc_Wave,圆弧波纹风格

PercentStyle_Rect,矩形风格
PercentStyle_Circle,圆形风格
PercentStyle_Ellipse,椭圆风格
PercentStyle_Cylinder,圆柱风格

barPosition,柱状条位置
BarPosition_Left,左侧显示
BarPosition_Right,右侧显示
BarPosition_Center,居中显示

tickPosition,刻度尺位置
TickPosition_Null,不显示
TickPosition_Left,左侧显示
TickPosition_Right,右侧显示
TickPosition_Both,两侧显示

pointStyle,坐标点样式
PointStyle_Rect,正方形
PointStyle_Circle,圆形

sliderStyle,滑块风格
SliderStyle_Line,线条状风格
SliderStyle_Circle,圆形状风格

arrowStyle,箭头样式
ArrowStyle_Left,向左箭头
ArrowStyle_Right,向右箭头
ArrowStyle_Top,向上箭头
ArrowStyle_Bottom,向下箭头

moveStyle,滚动样式
MoveStyle_LeftAndRight,左右来回滚动
MoveStyle_LeftToRight,从左滚动到右
MoveStyle_RightToLeft,从右滚动到左

osdFormat,OSD标签格式
OSDFormat_Text,文本
OSDFormat_Date,日期
OSDFormat_Time,时间
OSDFormat_DateTime,日期时间
OSDFormat_Image,图片

osdPosition,OSD标签位置
OSDPosition_Left_Top,左上角
OSDPosition_Left_Bottom,左下角
OSDPosition_Right_Top,右上角
OSDPosition_Right_Bottom,右下角

waveStyle,声音样式
WaveStyle_Line,线条样式
WaveStyle_Smooth,平滑样式
WaveStyle_Bar,柱状样式

#父类属性
geometry,区域

#自定义控件属性
minValue,最小值
maxValue,最大值
value,当前值
precision,精确度
step,步长
text,文字
unit,单位
title,标题
digitCount,数字长度
scalePrecision,刻度尺精确度
scaleMajor,大刻度数量
scaleMinor,小刻度数量
startAngle,起始旋转角度
endAngle,结束旋转角度
lineWidth,线条宽度
lineColor,线条颜色
borderRadius,边框圆角角度
borderMargin,边框边距
space,间距
radius,圆角角度
barBgColor,柱状背景颜色
barColor,柱状颜色
animation,启用动画
animationStep,动画步长
animationDuration,动画时长
animationType,动画类型
outerCircleColor,外圆背景颜色
innerCircleColor,内圆背景颜色
pieColorStart,饼圆开始颜色
pieColorMid,饼圆中间颜色
pieColorEnd,饼圆结束颜色
coverCircleColor,覆盖圆颜色
scaleColor,刻度尺颜色
pointerColor,指针颜色
centerCircleColor,中心圆颜色
textColor,文字颜色
bgColor,背景颜色
leftValue,左侧值
rightValue,右侧值
rangeTextColor,范围值颜色
doublePercent,对半等分
showValue,显示值
showOverlay,遮罩层
overlayColor,遮罩层颜色
progressColor,进度颜色
progressBgColor,进度背景颜色
circleColorStart,圆起始颜色
circleColorEnd,圆结束颜色
showPointer,显示指针
circleWidth,中间圆宽度
ringWidth,圆环宽度
ringPercentStart,起始圆环百分比
ringPercentMid,中间圆环百分比
ringPercentEnd,结束圆环百分比
ringColorStart,起始圆环颜色
ringColorMid,中间圆环颜色
ringColorEnd,结束圆环颜色
arcColor,圆弧颜色
scaleNumColor,刻度颜色
usedColor,进度颜色
freeColor,空余颜色
titleColor,标题颜色
darkColor,加深颜色
lightColor,高亮颜色
foreground,前景颜色
background,背景颜色
percentColor,百分比颜色
crownColorStart,滑盖起始颜色
crownColorEnd,滑盖结束颜色
bgColorStart,背景起始颜色
bgColorEnd,背景结束颜色
centerColorStart,中间圆起始颜色
centerColorEnd,中间圆结束颜色
northPointerColor,指北针颜色
southPointerColor,指南针颜色
pointerHourColor,时钟颜色
pointerMinColor,分钟颜色
pointerSecColor,秒钟颜色
ringColor,圆环颜色
triangleColor,三角形颜色
borderColor,边框颜色
borderWidth,边框宽度
northDotColor,指北点颜色
otherDotColor,其他点颜色
borderColor1,边框颜色1
borderColor2,边框颜色2
percentColor1,进度颜色1
percentColor2,进度颜色2
percentColor3,进度颜色3
baseColor,基准颜色
borderOutColorStart,外边框开始颜色
borderOutColorEnd,外边框结束颜色
borderInColorStart,里边框开始颜色
borderInColorEnd,里边框结束颜色
planeColor,姿态仪背景
glassColor,遮罩层颜色
handleColor,手柄颜色
degValue,旋转角度
rollValue,滚动值
angle,角度
valueTextColor,目标值文字颜色
valueBgColor,目标值背景颜色
outBgColor,外边框背景色
centerBgColorStart,圆环起始颜色
centerBgColorEnd,圆环结束颜色
showUserValue,显示用户值
userValue,用户值
userValueColor,用户值颜色
seconds,检测总时长
interval,时长进度更新间隔
enterColor,鼠标进入颜色
pressColor,鼠标按下颜色
iconText,四周图标值
centerText,中间图标值
autoRepeat,重复执行按下
autoRepeatDelay,延时执行按下动作
autoRepeatInterval,重复执行间隔

outerMaxValue,外圈最大值
outerMinValue,外圈最小值
outerValue,外圈值
outerCurrValue,外圈当前值
outerStartAngle,外环开始旋转角度
outerEndAngle,外环结束旋转角度
outerRingBgColor,外圈背景色
outerRingColor,外圈当前色
innerMaxValue,内圈最大值
innerMinValue,内圈最小值
innerValue,内圈值
innerCurrValue,内圈当前值
innerStartAngle,内环开始旋转角度
innerEndAngle,内环结束旋转角度
innerRingBgColor,内圈背景色
innerRingNegativeColor,内圈负值当前色
innerRingPositiveColor,内圈正值当前色
innerScaleMajor,内环大刻度数量
innerScaleMinor,内环小刻度数量
innerScaleColor,内环刻度颜色
innerScaleNumColor,内环刻度值颜色
precision,精确度
centerPixMapNegativeColor,中心图片颜色
centerPixMapPositiveColor,中心图片颜色
centerSvgPath,当前svg图片路径
outerValueTextColor,外环值文本颜色
innerNegativeValueTextColor,内环正值文本颜色
innerPositiveValueTextColor,内环负值文本颜色

nullPosition,起始角度
waterHeight,水波高度
waterDensity,水波密度
showPercent,显示百分比
showFree,显示未使用进度
showSmallCircle,显示小圆
clockWise,顺时针
borderOut,外边框
circleColor,圆颜色
innerBgColor,文本所在圆环的背景
outlinePenWidth,外边框宽度
dataPenWidth,数据文本宽度
format,格式
currentValue,当前值
reverse,反向
value1,值1
value2,值2
value3,值3
color1,颜色1
color2,颜色2
color3,颜色3
autoRadius,自动圆角角度
autoFont,自动字体大小
valueAll,总大小
valuePlay,已播放大小
valueLoad,已缓存大小
colorPlay,已播放颜色
colorLoad,缓存颜色
showProgressRadius,显示进度圆角角度
clipCenter,剪切中间部分
alarmMode,报警模式
ringPadding,圆环边距
ringBgColor,圆环背景颜色
ringValue1,环形值1
ringValue2,环形值2
ringValue3,环形值3
ringColor1,环形颜色1
ringColor2,环形颜色2
ringColor3,环形颜色3

bgColorStart,主背景开始颜色
bgColorEnd,主背景结束颜色
barColorStart,开始颜色
barColorMid,中间颜色
barColorEnd,结束颜色
textNormalColor,文字正常颜色
textSelectColor,文字选中颜色
items,所有条目集合
currentIndex,选中条目索引
currentItem,选中条目文字
bgRadius,背景圆角半径
barRadius,选中条目圆角
keyMove,按键移动
horizontal,横向显示
flat,扁平化
paddingLeft,文字左侧间隔
paddingRight,文字右侧间隔
paddingTop,文字顶部间隔
paddingBottom,文字底部间隔
showTriangle,显示倒三角
triangleLen,倒三角边长
triangleColor,倒三角颜色
showIcon,显示图标
iconSpace,图标间隔
iconSize,图标尺寸
iconNormal,正常图标
iconHover,悬停图标
iconCheck,选中图标
showLine,显示线条
lineSpace,线条间隔
lineWidth,线条宽度
lineColor,线条颜色

normalBgColor,正常背景颜色
hoverBgColor,悬停背景颜色
pressBgColor,按下背景颜色
pressedBgColor,按下背景颜色
checkBgColor,选中背景颜色
checkedBgColor,选中背景颜色

normalTextColor,正常文字颜色
hoverTextColor,悬停文字颜色
pressTextColor,按下文字颜色
pressedTextColor,按下文字颜色
checkTextColor,选中文字颜色
checkedTextColor,选中文字颜色

showArrow,显示箭头
arrowSize,箭头大小

items,节点集合
rightIconVisible,显示右侧图标
tipVisible,显示提示信息
tipWidth,提示信息宽度
separateVisible,显示行分隔符
separateHeight,行分隔符高度
separateColor,行分隔符颜色
lineLeft,线条左侧显示
lineVisible,显示线条
blurRadius,模糊半径
triangleLeft,三角形左侧显示
triangleVisible,显示三角形
triangleWidth,三角形宽度
triangleHeight,三角形高度
triangleRatio,三角形位置比例
triangleColor,三角形颜色
parentIconMargin,父节点图标边距
parentMargin,父节点边距
parentFontSize,父节点字体大小
parentHeight,父节点高度
parentBgNormalColor,父节点正常背景色
parentBgSelectedColor,父节点选中背景色
parentBgHoverColor,父节点悬停背景色
parentTextNormalColor,父节点正常文字颜色
parentTextSelectedColor,父节点选中文字颜色
parentTextHoverColor,父节点悬停文字颜色
childIconMargin,子节点图标边距
childMargin,子节点边距
childFontSize,子节点字体大小
childHeight,子节点高度
childBgNormalColor,子节点正常背景色
childBgSelectedColor,子节点选中背景色
childBgHoverColor,子节点悬停背景色
childTextNormalColor,子节点正常文字颜色
childTextSelectedColor,子节点选中文字颜色
childTextHoverColor,子节点悬停文字颜色
maxStep,最大步数
currentStep,当前第几步
currentBackground,当前背景色
currentForeground,当前前景色
lineLen,线条的长度
index,当前索引
texts,按钮文本集合
btnNormalColor,按钮正常颜色
btnHoverColor,按钮经过颜色
btnDarkColor,按钮加深颜色
textNormalColor,文字正常颜色
textHoverColor,文字经过颜色
textDarkColor,文字加深颜色
leftIcon,左侧图标
rightIcon1,右侧图标1
rightIcon2,右侧图标2
rightIcon3,右侧图标3
rightIcon4,右侧图标4
rightIcon5,右侧图标5
padding,间距
textCenter,文字居中
iconNormalColor,图标正常颜色
iconHoverColor,图标悬停颜色
iconPressColor,图标按下颜色
headHeight,顶部高度
xStep,X轴步长
yStep,Y轴步长
smooth,平滑效果
showHLine,横向线条
showVLine,纵向线条
showPoint,显示坐标点
showPointBg,显示坐标点背景
pointColor,坐标点颜色
deep,深度
longStep,长步长
shortStep,短步长
pointerWidth,指针宽度
sliderColorTop,滑块上部颜色
sliderColorBottom,滑块下部颜色
tipBgColor,当前值背景色
tipTextColor,当前值文字颜色
alarmValue,告警值
maxValueRight,右边最小值
minValueRight,右边最大值
valueRight,右边目标值
alarmValueRight,右边告警值
longStepRight,右边长步长
shortStepRight,右边短步长
alarmScaleColor,告警刻度颜色
coverColor,覆盖颜色
rulerTop,标尺在顶部
rangeValue,范围值
sliderColor,滑块颜色
btnPressColor,按钮按下颜色
showText,显示值
sliderBgPercent,滑块背景所占比例
sliderPercent,滑块所占比例
labTipWidth,气泡提示宽度
labTipHeight,气泡提示高度
labTipFont,气泡提示字体
showTime,显示时间
clickEnable,单击跳转
colorBg,背景颜色
colorValue,值颜色
colorLow,低值颜色
colorHigh,高值颜色
showColorName,显示颜色名字

autoHeight,自动高度
percentHeight,百分比高度
percentBorder,百分比边框宽度
percentRadius,百分比边框圆角
percentColor,百分比颜色
topColor,顶部颜色
bottomColor,底部颜色
disableColor,禁用颜色
staticMode,静态颜色模式
outMode,突出模式
hsbMode,显示HSB颜色
percent,百分比
color,颜色
barSpace,柱状间距
groupSpace,分组间距
cursorColor,鼠标颜色
hue,颜色分量HUE值
sat,颜色分量SAT值
columnCount,列数
showSuperText,显示角标
superText,角标文字
superTextFont,角标文字字体
superTextAlign,角标对齐方式
superTextColor,角标文字颜色
textFont,文字字体
normalColor,正常颜色
pressedColor,按下颜色
canMove,能移动
bgImage,背景图片
signColor,选中符号颜色
signSize,选中符号大小
checked,选中
hovered,永久悬停
bannerFixedSize,指示器固定尺寸
imageNames,图片集合
hoverStop,鼠标悬停停止
showNumber,显示序号
minHeight,指示器最小高度
minWidth,指示器最小宽度
maxWidth,指示器最大宽度
interval,间隔
navRadius,圆角角度
navColor,指示器颜色
tipColor,提示文字颜色
imageNames,图片名称
imageTips,提示信息
imageName,图片路径
normalImage,正常图片
enterImage,进入图片
leaveImage,离开图片
factor,动画因子
imageName1,图片1路径
imageName2,图片2路径
pixmap1,图片1
pixmap2,图片2
image,图片
imageValue,图片值
isChecked,选中
bottomSpace,底部间距
buttonSpace,按钮间距
icoSize,图标大小
fill,拉伸填充
fade,渐变显示
alarmColor,报警颜色
showRect,显示矩形
bgRadius,背景圆角角度
headRadius,头部圆角角度
borderColorStart,边框开始颜色
borderColorEnd,边框结束颜色
alarmColorStart,报警开始颜色
alarmColorEnd,报警结束颜色
normalColorStart,正常开始颜色
normalColorEnd,正常结束颜色
headColor,鱼头颜色
bodyColor,鱼身颜色
finColor,鱼鳍颜色
tailColor,鱼尾颜色
baseColor,基准颜色
finMove,鱼鳍摆动
speed,游动速度
wave,晃动幅度
currentAngle,旋转角度
headLen,鱼头尺寸
bodyLen,鱼身尺寸
finLen,鱼鳍尺寸
tailLen,鱼尾尺寸
bodyColor,身体颜色
eyeColor,眼睛颜色
eyeballColor,眼珠颜色
noseColor,鼻子颜色
earColor,耳朵颜色
tailColor,尾巴颜色
maxRadius,最大圆角
pieColor1Start,圆1开始颜色
pieColor1End,圆1结束颜色
pieColor2Start,圆2开始颜色
pieColor2End,圆2结束颜色
select,选中
showLunar,显示农历
bgImage,背景图片
date,当前日期
lunar,农历信息
borderColor,边框颜色
weekColor,周末颜色
superColor,角标颜色
lunarColor,农历节日颜色
currentTextColor,当前月文字颜色
otherTextColor,其他月文字颜色
selectTextColor,选中日期文字颜色
hoverTextColor,悬停日期文字颜色
currentLunarColor,当前月农历文字颜色
otherLunarColor,其他月农历文字颜色
selectLunarColor,选中日期农历文字颜色
hoverLunarColor,悬停日期农历文字颜色
currentBgColor,当前月背景颜色
otherBgColor,其他月背景颜色
selectBgColor,选中日期背景颜色
hoverBgColor,悬停日期背景颜色
weekTextColor,星期文字颜色
weekBgColor,星期背景颜色
bgPix,背景图片

names,姓名集合
types,类型集合
tels,电话集合
bgImage,背景图片
bgColor,背景颜色
telHighFontSize,高亮标签字体大小
telHighBgImage,高亮标签背景图片
telHighBgColor,高亮标签背景颜色
telHighTextColor,高亮标签文字颜色
telBannerBgColor,顶部字母导航背景颜色
telBannerTextColor,顶部字母导航文字颜色
telBannerLineColor,顶部字母导航线条颜色
telLetterNormalColor,右侧字母导航正常颜色
telLetterHighColor,右侧字母导航高亮颜色
telButtonBgColor,通讯录按钮背景颜色
telButtonNameColor,通讯录按钮姓名颜色
telButtonTypeColor,通讯录按钮类型颜色
telPanelNormalColor,滚动条正常颜色
telPanelHighColor,滚动条高亮颜色
dotColor,点颜色
dotRadius,点圆角角度
polygonColor,多边形颜色
selectColor,选中颜色
selectDotVisible,选中点可见
online,限定在线内
rockerRadius,遥感圆半径
rockerColor,遥感颜色

showTitle,显示标题
titleHeight,标题高度
titleFontSize,标题字号
title,标题
showLegend,显示图例
legendHeight,图例高度
legendFontSize,图例字号
bgColor,背景颜色
textColor,文字颜色
highColor,高亮颜色
flagColor,标识颜色
outCircleColor,外圆颜色
midCircleColor,中间圆颜色
inCircleColor,里边圆颜色
outPieInfos,外边饼图数据
inPieInfos,里边饼图数据
lineStep,线条步长
lineSpeed,线条速度
itemWidth,元素宽度
itemHeight,元素高度
autoWidth,自动宽度
dotHide,隐藏点
number,数字
numberColorStart,数字开始颜色
numberColorEnd,数字结束颜色
numberBgColorStart,背景开始颜色
numberBgColorEnd,背景结束颜色
chunkColor1,进度颜色1
chunkColor2,进度颜色2
chunkColor3,进度颜色3
textColor1,文字颜色1
textColor2,文字颜色2
textColor3,文字颜色3
sleep,文本停留间隔
staticText,静态文本
textSize,文本大小
mouseHoverStop,鼠标悬停停止
titleHeight,标题高度
titleText,标题文字
titleFont,标题字体
titleAlignment,标题对齐方式
titleColor,标题颜色
titleDisableColor,禁用文字颜色
borderDisableColor,禁用边框颜色
alarmInterval,报警切换间隔
alarmTextColor,报警文字颜色
alarmDarkColor,报警加深颜色
alarmNormalColor,报警普通颜色
isAlarm,报警
isEnable,启用
margin,边距
codeLen,验证码长度
noiseCount,干扰点数量
lineCount,干扰线条数量
zoomFactor,缩放因子
penColor,画笔颜色
iconImage,图标图片
ip,IP地址
drawPoint,绘制坐标点
pointSize,坐标点尺寸
int labWidth,左侧宽度
textCh1,通道1文字
textCh2,通道2文字
textCh3,通道3文字
textCh4,通道4文字
videoTextColor,顶部文字颜色
videoBgColor,顶部背景颜色
videoChColor,通道背景颜色
videoDataColor,通道数据颜色
copyImage,拷贝图片
checkLive,检测活着
drawImage,绘制图片
fillImage,拉伸填充
flowEnable,显示悬浮条
flowBgColor,悬浮条背景颜色
flowPressColor,悬浮条按下颜色
timeout,超时时间
focusColor,有焦点边框颜色
bgText,显示文字
bgImage,背景图片
osd1Visible,显示标签1
osd1FontSize,标签1字号
osd1Text,标签1文本
osd1Color,标签1颜色
osd1Image,标签1图片
osd1Format,标签1文本格式
osd1Position,标签1位置
osd2Visible,显示标签2
osd2FontSize,标签2字号
osd2Text,标签2文本
osd2Color,标签2颜色
osd2Image,标签2图片
osd2Format,标签2文本格式
osd2Position,标签2位置
grooveColor,滑过的颜色
handleBorderColor,手柄边框颜色
sliderHeight,滑块高度
itemTexts,节点文本集合
normalPixmap,正常图标
selectPixmap,选中图标
hoverPixmap,悬停图标
itemCount,节点个数
itemWidth,节点宽度
itemHeight,节点高度
itemColor,节点颜色
scrollWidth,滚动条宽度
scrollLeft,滚动条在左侧
scrollColor,滚动条颜色
enableEdit,编辑菜单启用
enableAdd,添加菜单启用
enableDelete,删除菜单启用
scrollWidth,滚动条宽度
scrollLeft,滚动条在左侧
scrollColor,滚动条颜色
valueColor,值颜色
imageBorderWidth,图片边框宽度
imageBorderColor,图片边框颜色
scanRadius,扫描线半径
scanWidth,扫描线边框宽度
scanStep,扫描线步长
ringStep,扩散圈步长
scanColor,扫描线颜色
damping,衰减系数
refractive,折射系数
stoneSize,石头大小
stoneWeight,石头重量
arcWidth,圆弧宽度
freeColor|GaugeEdit,进度背景
infos,信息集合 
hoverOffset,悬停偏移值
hoverAlpha,悬停透明度
valueBrush,值画刷
shadowColor,光晕颜色
shadowWidth,光晕宽度
radiusWidth,半径宽度
itemMargin,节点边距
itemHeight,节点高度
infoPadding,信息距离
infoHeight,信息高度
hourColor,时钟颜色
minuteColor,分钟颜色
secondColor,秒钟颜色
gridColor,表格颜色
headerTextColor,表头文本颜色
headerBgColor,表头背景颜色
scaled,缩放比例
lineHeight,线条高度
backColor,背景颜色
btnPageCount,页码按钮数量
spacing,边距
fontSize,字体大小

#增补部分
borderPadding,边框边距
rectRadius,圆角
bgColorOff,背景禁用颜色
bgColorOn,背景正常颜色
sliderColorOff,滑块禁用颜色
sliderColorOn,滑块正常颜色
textColorOff,文字禁用颜色
textColorOn,文字正常颜色
textOff,关闭状态文字
textOn,开启状态文字
showStyle,应用样式
showLabInfo,显示信息标签
showGoPage,显示跳转按钮
pageButtonCount,页码按钮数量
smoothType,平滑算法类型
dataColor,数据颜色1
dataColor2,数据颜色2
dataColor3,数据颜色3
labWidth,标签宽度
btnSize,按钮尺寸
stepSingle,单步移动距离
stepDouble,多步移动距离
circleColor1,中心园颜色1
circleColor2,中心园颜色2

#飞控套件
roll,翻转角度
pitch,倾斜角度
altitude,海拔高度
pressure,大气压强
airspeed,航行速度
heading,头顶角度
headingBug,头顶指示
course,飞行航向
bearing,飞行方位
bearingVisible,飞行方位可见
deviation,飞行偏差
deviationVisible,飞行偏差可见
distance,飞行距离
distanceVisible,飞行距离可见
turnRate,圆周率
slipSkid,滑行
climbRate,爬升速度

#2022年新增控件
fanColor1,扇形颜色1
fanColor2,扇形颜色2
fanSize,扇形尺寸
fanOffset,扇形偏移
fanCount,扇形数量
fanText,扇形文本集合
levelCount,层级数量
sideCount,边数量
sideColor,边颜色
sideText,种类描述
startAngle,开始角度
scalePos,标尺位置
scaleVisible,标尺可见
legendVisible,图例可见
colorNormal,正常颜色
colorAlarm,报警颜色

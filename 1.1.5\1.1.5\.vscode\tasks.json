{
    "version": "2.0.0",
    

    "tasks": [
        {
            "label": "echo",
            "type": "shell",
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "command": "echo",
            "args": [
                "${env:OS}"
            ]
        },

        {
            "label": "mkdir",
            "type": "shell",
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "command": "mkdir",
            "args": [
                "-Force",
                "build"
            ]
        },
        {
            "label": "qmake-debug",
            "type": "shell",
            "options": {
                "cwd": "${workspaceFolder}/build"
            },
            "command": "qmake",
            "args": [
                "${workspaceFolder}/ccc/ccc.pro",
                "-spec",
                "win32-g++",
                "CONFIG+=debug",
                "CONFIG+=qml_debug",
                "CONFIG+=console"
            ],
            "dependsOn": [
                "mkdir"
            ]
        },
        {
            "label": "make-debug",
            "type": "shell",
            "options": {
                "cwd": "${workspaceFolder}/build"
            },
            "command": "mingw32-make",
            "args": [
                "-f",
                "Makefile.Debug",
                "-j8"
            ],
            "dependsOn": [
                "qmake-debug"
            ]
        },
        {
            "label": "run-debug",
            "type": "process",
            "options": {
                "cwd": "${workspaceFolder}/bin"
            },
            "command": "ccc.exe",
            "dependsOn": [
                "make-debug"
            ],
            "problemMatcher": []
        },
        {
            "label": "qmake-release",
            "type": "shell",
            "options": {
                "cwd": "${workspaceFolder}/build"
            },
            "command": "qmake",
            "args": [
                "${workspaceFolder}/ccc/ccc.pro",
                "-spec",
                "win32-g++",
                "CONFIG+=release"
            ],
            "dependsOn": [
                "mkdir"
            ],
            "problemMatcher": []
        },
        {
            "label": "make-release",
            "type": "shell",
            "options": {
                "cwd": "${workspaceFolder}/build"
            },
            "command": "mingw32-make",
            "args": [
                "-f",
                "Makefile.Release",
                "-j8"
            ],
            "dependsOn": [
                "qmake-release"
            ],
            "problemMatcher": []
        },
        {
            "label": "run-release",
            "type": "process",
            "options": {
                "cwd": "${workspaceFolder}/bin"
            },
            "command": "ccc.exe",
            "dependsOn": [
                "make-release"
            ],
            "problemMatcher": []
        },
        {
            "label": "clean",
            "type": "shell",
            "options": {
                "cwd": "${workspaceFolder}/build"
            },
            "command": "mingw32-make",
            "args": [
                "clean"
            ],
            "problemMatcher": []
        },
        {
            "label": "clear-all",
            "type": "shell",
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "command": "del bin/ccc.exe",
            "dependsOn": [
                "clean"
            ],
            "problemMatcher": []
        }
    ],
}



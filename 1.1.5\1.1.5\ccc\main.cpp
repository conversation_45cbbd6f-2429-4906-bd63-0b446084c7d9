#include <QApplication>
#include <QWidget>
#include <QDebug>

#include "backend.h"
#include "main_win_less.h"

#ifdef Q_OS_WIN32
#include "QBreakpadHandler.h"
#include "QBreakpadHttpUploader.h"
#endif

#ifdef Q_OS_LINUX
#include "client/linux/handler/exception_handler.h"
#include "client/linux/handler/minidump_descriptor.h"
using namespace google_breakpad;
bool MiniDumpPrintInfo(const MinidumpDescriptor &descriptor, void* context, bool succeeded)
{
    if (succeeded)
    {
        qDebug() << "Mini Dump File:" << descriptor.path();
    }
    return succeeded;
}
#endif

void BridgeWinLess(MainWinLess *win, Backend *end);

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);

    app.setWindowIcon(QIcon(":/main.ico"));

#ifdef Q_OS_WIN32
    QBreakpadInstance.setDumpPath("crashes");
#endif

#ifdef Q_OS_LINUX
    MinidumpDescriptor descriptor("crashes");
    ExceptionHandler exception_handle(descriptor, NULL, MiniDumpPrintInfo, NULL, true, -1);
#endif

    Backend *end = new Backend;
    MainWinLess *win = new MainWinLess;
    BridgeWinLess(win, end);
    win->show();
    
    return app.exec();
}

void BridgeWinLess(MainWinLess *win, Backend *end)
{
    // Open Ports
    QObject::connect(win->sub_win_set->sub_win_set_sys, SIGNAL(SerConnectClicked(QStringList)), end->ser_manage, SLOT(ConnectPort(QStringList)));
    QObject::connect(end->ser_manage, SIGNAL(SerConnectStatus(QString)), win, SLOT(SetStatus(QString)));

    // Read Params and Set Params, Current
    QObject::connect(win->sub_win_set->sub_win_set_sys, SIGNAL(SerSendCmd(QByteArray)), end->ser_manage, SLOT(SlotSendCmdCurrent(QByteArray)));
    QObject::connect(win->sub_win_set->sub_win_current, SIGNAL(SerSendCmd(QByteArray)), end->ser_manage, SLOT(SlotSendCmdCurrent(QByteArray)));
    QObject::connect(win->sub_win_set->sub_win_micro, SIGNAL(SerSendCmd(QByteArray)), end->ser_manage, SLOT(SlotSendCmdCurrent(QByteArray)));
    QObject::connect(win->sub_win_set->sub_win_power, SIGNAL(SerSendCmd(QByteArray)), end->ser_manage, SLOT(SlotSendCmdCurrent(QByteArray)));
    QObject::connect(win->sub_win_set->sub_win_current, SIGNAL(CtrlMeasureStatus(bool)), end->ser_manage, SLOT(SlotCtrlMeasureCurrent(bool)));
    QObject::connect(win->sub_win_set->sub_win_cool, SIGNAL(SerSendCmd(QByteArray)), end->ser_manage, SLOT(SlotSendCmdCooling(QByteArray)));  // 0722 新增

    // 0723 保存配置 导入配置 信号与槽连接
    QObject::connect(win->sub_win_set->sub_win_set_sys, SIGNAL(SaveSetSysParams(QMap<ushort, QByteArray>)), end, SLOT(SlotSaveParamsSetSys(QMap<ushort, QByteArray>)));
    QObject::connect(win->sub_win_set->sub_win_set_sys, SIGNAL(LoadSetSysParams()), end, SLOT(SlotLoadParamsSetSys()));
    QObject::connect(end, SIGNAL(LoadedParamsSetSys(QMap<ushort, QByteArray>)), win->sub_win_set->sub_win_set_sys, SLOT(UpdateLoadedParamsSetSys(QMap<ushort, QByteArray>)));
    // 0724
    QObject::connect(win->sub_win_set->sub_win_current, SIGNAL(SaveCurrentParams(QMap<ushort, QByteArray>)), end, SLOT(SlotSaveParamsCurrent(QMap<ushort, QByteArray>)));
    QObject::connect(win->sub_win_set->sub_win_current, SIGNAL(LoadCurrentParams()), end, SLOT(SlotLoadParamsCurrent()));
    QObject::connect(end, SIGNAL(LoadedParamsCurrent(QMap<ushort, QByteArray>)), win->sub_win_set->sub_win_current, SLOT(UpdateLoadedParams(QMap<ushort, QByteArray>)));
    // 0728
    QObject::connect(win->sub_win_set->sub_win_set_sys, SIGNAL(SaveFeedbackParams(QMap<ushort, QByteArray>)), end, SLOT(SlotSaveParamsFeedback(QMap<ushort, QByteArray>)));
    QObject::connect(win->sub_win_set->sub_win_set_sys, SIGNAL(LoadFeedbackParams()), end, SLOT(SlotLoadParamsFeedback()));
    QObject::connect(end, SIGNAL(LoadedParamsFeedback(QMap<ushort, QByteArray>)), win->sub_win_set->sub_win_set_sys, SLOT(UpdateLoadedParamsFeedback(QMap<ushort, QByteArray>)));
    // 0729
    QObject::connect(win->sub_win_set->sub_win_micro, SIGNAL(SaveMicroParams(QMap<ushort, QByteArray>)), end, SLOT(SlotSaveParamsMicro(QMap<ushort, QByteArray>)));
    QObject::connect(win->sub_win_set->sub_win_micro, SIGNAL(LoadMicroParams()), end, SLOT(SlotLoadParamsMicro()));
    QObject::connect(end, SIGNAL(LoadedParamsMicro(QMap<ushort, QByteArray>)), win->sub_win_set->sub_win_micro, SLOT(UpdateLoadedParams(QMap<ushort, QByteArray>)));
    // 0729
    QObject::connect(win->sub_win_set->sub_win_power, SIGNAL(SavePowerParams(QMap<ushort, QByteArray>)), end, SLOT(SlotSaveParamsPower(QMap<ushort, QByteArray>)));
    QObject::connect(win->sub_win_set->sub_win_power, SIGNAL(LoadPowerParams()), end, SLOT(SlotLoadParamsPower()));
    QObject::connect(end, SIGNAL(LoadedParamsPower(QMap<ushort, QByteArray>)), win->sub_win_set->sub_win_power, SLOT(UpdateLoadedParams(QMap<ushort, QByteArray>)));

    // Read Params and Set Params, Feedback
    QObject::connect(win->sub_win_set->sub_win_set_sys, SIGNAL(SerSendCmdFeedback(QByteArray)), end->ser_manage, SLOT(SlotSendCmdFeedback(QByteArray)));

    // Read Params and Set Params, Cooling
    // QObject::connect(win->sub_win_plot->plot_cool, SIGNAL(SerSendCmd(QByteArray)), end->ser_manage, SLOT(SlotSendCmdCooling(QByteArray)));

    // Show Status, Measure Params, Set Params, Micro Params, Measure Result
    QObject::connect(end->ser_manage, SIGNAL(CurrentStatus(uchar, uchar)), win->sub_win_set->sub_win_set_sys, SLOT(UpdateDevInfo(uchar, uchar)));
    QObject::connect(end->ser_manage, SIGNAL(CurrentMeasureParams(ushort, QByteArray)), win->sub_win_set->sub_win_set_sys, SLOT(UpdateMeasureParams(ushort, QByteArray)));
    QObject::connect(end->ser_manage, SIGNAL(CurrentSetParams(ushort, QByteArray)), win->sub_win_set->sub_win_current, SLOT(UpdateSetParams(ushort, QByteArray)));
    QObject::connect(end->ser_manage, SIGNAL(CurrentMicroParams(ushort, QByteArray)), win->sub_win_set->sub_win_micro, SLOT(UpdateMicroParams(ushort, QByteArray)));
    QObject::connect(end->ser_manage, SIGNAL(CurrentPowerParams(ushort, QByteArray)), win->sub_win_set->sub_win_power, SLOT(UpdatePowerParams(ushort, QByteArray)));
    // QObject::connect(end->ser_manage, SIGNAL(CurrentMeasureResult(ushort, QByteArray)), win->sub_win_plot->plot_ccc, SLOT(UpdateMeasureData(ushort, QByteArray)));
    QObject::connect(end->ser_manage, SIGNAL(CurrentMeasureParams(ushort, QByteArray)), win->sub_win_plot->plot_ccc, SLOT(UpdateMeasureData(ushort, QByteArray)));
    QObject::connect(end->ser_manage, SIGNAL(CurrentSetParams(ushort, QByteArray)), win->sub_win_plot->plot_ccc, SLOT(UpdateMeasureData(ushort, QByteArray)));

    // Show Status, Params
    QObject::connect(end->ser_manage, SIGNAL(FeedbackParams(ushort, QByteArray)), win->sub_win_set->sub_win_set_sys, SLOT(UpdateFeedbackParams(ushort, QByteArray)));

    // Show Status, Temperature, Liquid Level, Pressure, Current
    QObject::connect(end->ser_manage, SIGNAL(CoolingStatus(uchar, uchar)), win->sub_win_set->sub_win_set_sys, SLOT(UpdateDevInfo(uchar, uchar)));
    //QObject::connect(end->ser_manage, SIGNAL(CoolingValue(uchar, short)), win->sub_win_plot->plot_cool, SLOT(UpdateValue(uchar, short)));

    
    // 0818
    QObject::connect(win->sub_win_set->sub_win_current, SIGNAL(UpdatePlotCCC()), win->sub_win_plot->plot_ccc, SLOT(InitCCCSetting()));

    // 0723 保存Cool数据
    QObject::connect(win->sub_win_set->sub_win_cool, SIGNAL(StartSaveSignal(int)), end->data_handle, SLOT(StartSaveCoolData(int)));
    QObject::connect(win->sub_win_set->sub_win_cool, SIGNAL(StopSaveSignal()), end->data_handle, SLOT(StopSaveCoolData()));
    // 0723 保存CCC数据
    QObject::connect(win->sub_win_set->sub_win_current, SIGNAL(StartSaveSignal(int)), end->data_handle, SLOT(StartSaveCCCData(int)));
    QObject::connect(win->sub_win_set->sub_win_current, SIGNAL(StopSaveSignal()), end->data_handle, SLOT(StopSaveCCCData()));

    // 0811 获取 edit_sample 
    QObject::connect(win->sub_win_set->sub_win_set_sys, SIGNAL(SendEditSample(int)), end->data_handle, SLOT(UpdateEditSample(int)));

    // 0811 CCC 数据处理
    QObject::connect(win->sub_win_plot->plot_ccc, SIGNAL(SignalResetCCCStats()), end->data_handle, SLOT(ResetCCCStats()));
    QObject::connect(win->sub_win_plot->plot_ccc, SIGNAL(SignalUpdateStatMode(int)), end->data_handle, SLOT(ChangeStatMode(int)));
    QObject::connect(end->data_handle, SIGNAL(CCCStatsRatioRes(double, double)), win->sub_win_plot->plot_ccc, SLOT(OnCCCStatsRatioRes(double, double)));
    QObject::connect(end->data_handle, SIGNAL(CCCRatioMinMax(double, double)), win->sub_win_plot->plot_ccc, SLOT(OnCCCRatioMinMax(double, double)));
    QObject::connect(end->data_handle, SIGNAL(CCCResMinMax(double, double)), win->sub_win_plot->plot_ccc, SLOT(OnCCCResMinMax(double, double)));

    // 0815 Cool原始数据处理后发送给前端
    QObject::connect(end->data_handle, SIGNAL(CoolValueConvertedTemp(QVector<double>,QVector<double>)), win->sub_win_plot->plot_cool, SLOT(PlotTemp(QVector<double>,QVector<double>)));
    QObject::connect(end->data_handle, SIGNAL(CoolValueConvertedLiquid(QVector<double>,QVector<double>)), win->sub_win_plot->plot_cool, SLOT(PlotLiquid(QVector<double>,QVector<double>)));
    QObject::connect(end->data_handle, SIGNAL(CoolValueConvertedPress(QVector<double>,QVector<double>)), win->sub_win_plot->plot_cool, SLOT(PlotPress(QVector<double>,QVector<double>)));
    QObject::connect(end->data_handle, SIGNAL(CoolValueConvertedCurrent(QVector<double>,QVector<double>)), win->sub_win_plot->plot_cool, SLOT(PlotCurrent(QVector<double>,QVector<double>)));
    QObject::connect(end->data_handle, SIGNAL(CoolValueConverted(uchar,double)), win->sub_win_plot->plot_cool, SLOT(OnCoolConverted(uchar,double)));
    // 0816 CCC原始数据处理后发送给前端
    QObject::connect(end->data_handle, SIGNAL(CCCValueConvertedRatio(QVector<double>,QVector<double>)), win->sub_win_plot->plot_ccc, SLOT(PlotRatio(QVector<double>,QVector<double>)));
    QObject::connect(end->data_handle, SIGNAL(CCCValueConvertedRes(QVector<double>,QVector<double>)), win->sub_win_plot->plot_ccc, SLOT(PlotRes(QVector<double>,QVector<double>)));
    QObject::connect(end->data_handle, SIGNAL(CCCValueConverted(double,double)), win->sub_win_plot->plot_ccc, SLOT(OnCCCValueConverted(double,double)));
    QObject::connect(end->data_handle, SIGNAL(CCCRunTimeMeasure(bool)), win->sub_win_plot->plot_ccc, SLOT(StartStopMeasure(bool)));

}

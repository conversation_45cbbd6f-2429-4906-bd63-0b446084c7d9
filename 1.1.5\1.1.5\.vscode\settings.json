{
    "files.associations": {
        "qwidget": "cpp",
        "qapplication": "cpp",
        "qtime": "cpp",
        "qtmath": "cpp",
        "qpushbutton": "cpp",
        "qtextcodec": "cpp",
        "qdir": "cpp",
        "qthread": "cpp",
        "qdebug": "cpp",
        "qfile": "cpp",
        "qgridlayout": "cpp",
        "new": "cpp",
        "qvboxlayout": "cpp",
        "qhboxlayout": "cpp",
        "qobject": "cpp",
        "qicon": "cpp",
        "qgroupbox": "cpp",
        "fstream": "cpp",
        "array": "cpp",
        "string": "cpp",
        "chrono": "cpp",
        "algorithm": "cpp",
        "qdatastream": "cpp",
        "qvector": "cpp",
        "*.tcc": "cpp",
        "complex": "cpp",
        "typeinfo": "cpp",
        "qudpsocket": "cpp",
        "atomic": "cpp",
        "cctype": "cpp",
        "clocale": "cpp",
        "cmath": "cpp",
        "cstdarg": "cpp",
        "cstddef": "cpp",
        "cstdint": "cpp",
        "cstdio": "cpp",
        "cstdlib": "cpp",
        "cstring": "cpp",
        "ctime": "cpp",
        "cwchar": "cpp",
        "cwctype": "cpp",
        "deque": "cpp",
        "list": "cpp",
        "unordered_map": "cpp",
        "vector": "cpp",
        "exception": "cpp",
        "functional": "cpp",
        "ratio": "cpp",
        "system_error": "cpp",
        "tuple": "cpp",
        "type_traits": "cpp",
        "initializer_list": "cpp",
        "iosfwd": "cpp",
        "iostream": "cpp",
        "istream": "cpp",
        "limits": "cpp",
        "memory": "cpp",
        "ostream": "cpp",
        "numeric": "cpp",
        "sstream": "cpp",
        "stdexcept": "cpp",
        "streambuf": "cpp",
        "thread": "cpp",
        "cinttypes": "cpp",
        "utility": "cpp",
        "qlist": "cpp",
        "random": "cpp",
        "qnetworkaccessmanager": "cpp",
        "qhostinfo": "cpp",
        "qlabel": "cpp"
    },

    
}
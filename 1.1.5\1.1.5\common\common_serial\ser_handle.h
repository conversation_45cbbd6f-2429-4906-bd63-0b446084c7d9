#ifndef SER_HANDLE_H
#define SER_HANDLE_H

#include <QObject>
#include <QThread>
#include <QDebug>
#include <QQueue>
#include <QTimer>

/**********************************************************
                         串口接收类
**********************************************************/
class SerProtoRx : public QObject
{
    Q_OBJECT

public:
    explicit SerProtoRx(uchar pro);
    ~SerProtoRx();

    void SetRxProto(uchar pro);

    bool StandProtoRx(QByteArray dataReceived);
    void UnLenProtoRx(QByteArray dataReceived);
    void SimpleProtoRx(QByteArray dataReceived);
    void FIDProtoRx(QByteArray data_recived);
    void IonProtoRx(QByteArray data_recived);
    void CccCurrentProtoRx(QByteArray data_recived);
    void CccCoolingProtoRx(QByteArray data_recived);

private:
    uchar CrcSum(QByteArray data, uint startIndex, uint endIndex);

public slots:
    bool DataRecieveSer(QByteArray dataReceived);

public:
    uchar proto;            //接收协议
    QByteArray dataSerRec;  //完整数据帧
    uint lenSerRec;         //完整帧帧长

private:
    QThread thread;

signals:
    void SignalSerFrame(QByteArray, int);
};

/**********************************************************
                         串口发送类
**********************************************************/
class SerProtoTx : public QObject
{
    Q_OBJECT

public:
    explicit SerProtoTx();
    ~SerProtoTx();

    QByteArray StandProtoTx(QByteArray cmd, QByteArray src, QByteArray dst);     //标准协议发送
    QByteArray SimpleProtoTx(QByteArray cmd);    //简化协议发送
    QByteArray UnLenProtoTx(QByteArray cmd);     //不定长协议发送

    QByteArray CccCurrentProtoTx(QByteArray cmd);   // CCC Current Protocol Send
    QByteArray CccCoolingProtoTx(QByteArray cmd);   // CCC Cooling Protocol Send

private:
    uchar CrcSum(QByteArray data, uint startIndex, uint endIndex);
    ushort CrcSum16(QByteArray data, uint startIndex, uint endIndex);
    uchar Crc8(QByteArray data, uint start, uint end, uchar poly);
    QByteArray HexToAscii(const QByteArray &hexData);  // 0718 十六进制转ASCII

private:
    QThread thread;

    QQueue <QByteArray> queue_cmd;      // Queue for Command Buffer
    QByteArray cmd_now;
    QTimer *timer_send;
    int send_period = 200;
    bool is_sending = false;

signals:
    void SignalSerFrameTx(QByteArray);  // Signal To Send Frame to Serial Port
    void SignalSerFrameOver();          // Signal To Indicate Frame Sending is Over

public slots:
    void InitThread();
    void SlotAddCmdToQueue(QByteArray cmd, uchar proto);   // Add Command to Queue
    void SlotSendCmdFromQueue();        // Get Command from Queue to Send
    void SetTimerSendPeriod(int period);// Set Send Period
};

#endif // SER_HANDLE_H
